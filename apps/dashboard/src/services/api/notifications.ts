import api from "@/services/api";
import { settingsService } from "./settings";
import { isBooleanObject } from "util/types";

const log = (label: string, err: unknown) => console.error(label, err);

export interface Notification {
  orderBy: string;
  pageNumber: number;
  id: string;
  createdAt: string;
  textContent: Record<string, string>;
  read: boolean;
  regionId: string;
  subscriptionId: string | null;
  subscription: any | null;
  type: string;
  subtype: string;
}

export const notificationService = {
  getAll: async (params: Record<string, unknown>) => {
    try {
      // Convert params to query string
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if(typeof value === "object") {
            queryParams.append(key, encodeURIComponent(JSON.stringify(value)))
          }else {
            queryParams.append(key, String(value));
          }
        }
      });
      
      const queryString = queryParams.toString();
      const url = queryString ? `/notification?${queryString}` : '/notification';
      
      return await api(url, { method: "GET" });
    } catch (e) {
      log("Error fetching notifications:", e);
      throw e;
    }
  },

  getById: async (id: string) => {
    try {
      return await api(`/notification/${id}`, { method: "GET" });
    } catch (e) {
      log(`Error fetching notification ${id}:`, e);
      throw e;
    }
  },

  count: async (read: boolean, archived: boolean) => {
    try {
      const result = await api(
        `/notification/count?unread=${read}&archived=${archived}`,
        { method: "GET" }
      );
      return result?.count || 0;
    } catch (e) {
      log("Error fetching notification count:", e);
      throw e;
    }
  },

  markAsRead: async (id: string, tag: string = "READ") => {
    try {
      const actualTag = !tag ? "READ" : tag;
      return await api(`/notification/${id}/read?tag=${actualTag}`, { method: "PATCH" });
    } catch (e) {
      log(`Error updating notification status for ${id}:`, e);
      throw e;
    }
  },

  getCategories: async () => {
    try {
      return await settingsService.getGlobalSetting("notifications.categories");
    } catch (e) {
      log("Error fetching notification categories:", e);
      throw e;
    }
  },
};

export default notificationService;
