import { DetailSubscription } from "@/features/subscriptions/pages/detail";
import {
  cancelComplaint,
  createComplaint,
  fetchComplaintById,
  fetchComplaints,
  fetchDocument,
  markDocumentAsDone,
  uploadDocument,
} from "@/services/api/complaints";
import type { ApiQueryParams, PagedResponse } from "@/services/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import type { ComplaintRequestType } from "../components/columns";
import type { ComplaintsSchema } from "./schema";

export interface ComplaintPayload {
  body: string;
  category: string;
  subcategory: string;
  subscriptionId: string;
  files: string[];
}

export function useComplaint(id: string | undefined) {
  const query = useQuery({
    queryKey: ["complaint", id],
    queryFn: async (): Promise<ComplaintRequestType> => {
      if (!id) throw new Error("Complaint ID is required");
      return fetchComplaintById(id);
    },
    enabled: !!id,
    staleTime: Infinity,
  });

  return useMemo(() => query.data, [query.data]);
}

export function useComplaints(
  page = 1,
  pageSize = 10,
  options?: Partial<ApiQueryParams>
) {
  const queryParams: ApiQueryParams = {
    pageNumber: Math.max(page, 1),
    pageSize,
    orderBy: "createdAt:desc",
    ...options,
  };

  return useQuery({
    queryKey: ["complaints", queryParams],
    queryFn: async (): Promise<PagedResponse<ComplaintRequestType>> => {
      return fetchComplaints(queryParams);
    },
    staleTime: Infinity,
  });
}

export function useDocuments(documentIds: string[]) {
  return useQuery({
    queryKey: ["documents", ...documentIds],
    queryFn: async () => {
      const promises = documentIds.map((id) => fetchDocument(id));
      return Promise.all(promises);
    },
    enabled: documentIds.length > 0,
  });
}

export async function getComplaintRequests(
  page = 1,
  pageSize = 10,
  options?: Partial<ApiQueryParams>
): Promise<ComplaintRequestType[]> {
  const queryParams: ApiQueryParams = {
    pageNumber: Math.max(page, 1),
    pageSize,
    ...options,
  };

  const response = await fetchComplaints(queryParams);
  return response.content || [];
}

export function useCreateComplaint() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ComplaintsSchema) => {
      let fileIds: string[] = [];

      if (Array.isArray(data.file)) {
        fileIds = data.file.filter((id) => typeof id === "string") as string[];
      } else if (typeof data.file === "string") {
        fileIds = [data.file];
      }

      const payload: ComplaintPayload = {
        body: data.applicationDescription,
        category: data.applicationType,
        subcategory: data.applicationCategory,
        subscriptionId: data.subscriptionId,
        files: fileIds,
      };

      const d = await createComplaint(payload);

      queryClient.invalidateQueries({ queryKey: ["complaints"] });

      return d;
    },
  });
}

export function useSubscriptionById(id: string) {
  return useQuery({
    queryKey: ["subscription", id],
    queryFn: () => fetchSubscriptionById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 2
  });
}

async function fetchSubscriptionById(id: string): Promise<DetailSubscription> {
  try {
    const response = await fetch(`/api/subscription/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch subscription: ${response.statusText}`);
    }
    return response.json();
  } catch (error) {
    console.error(`Error fetching subscription by ID (${id}):`, error);
    throw error;
  }
}

export function useUploadDocument() {
  return useMutation({
    mutationFn: async (file: File) => {
      return uploadDocument(file);
    },
  });
}

export function useMarkDocumentAsDone() {
  return useMutation({
    mutationFn: async (fileId: string) => {
      return markDocumentAsDone(fileId);
    },
  });
}

export function useCancelComplaint() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (complaintId: string) => {
      return cancelComplaint(complaintId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["complaints"] });
      
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ["complaints"] });
      }, 0);
    },
  });
}

export function useComplaintCategories(includeDeleted = true) {
  return useQuery({
    queryKey: ["complaintCategories", includeDeleted],
    queryFn: async () => {
      const categories = (await getComplaintCategories()) as any; // TODO: typing

      console.log(categories)

      if (includeDeleted) return categories;

      // 1⃣ remove deleted categories
      const withoutDeletedCategories = Object.fromEntries(
        Object.entries(categories ?? {}).filter(
          ([, data]) => (data as any).deleted !== "true"
        )
      );

      // 2⃣ remove deleted sub-categories and drop parent categories that end up empty
      const cleaned = Object.fromEntries(
        Object.entries(withoutDeletedCategories)
          .map(([key, data]) => {
            const keptSubcats = Object.fromEntries(
              Object.entries((data as any).subcategories ?? {}).filter(
                ([, sub]) => (sub as any).deleted !== "true"
              )
            );

            return Object.keys(keptSubcats).length === 0
              ? null
              : [
                  key,
                  {
                    ...(data as any),
                    subcategories: keptSubcats,
                  },
                ];
          })
          .filter(Boolean) as [string, any][]
      );

      return cleaned;
    },
  });
}

export const processComplaintData = (data: {
  body: string;
  category: string;
  subcategory: string;
  subscriptionId: string;
  files?: string[];
}) => {
  const processedData = {
    body: data.body,
    category: data.category,
    subcategory: data.subcategory,
    subscriptionId: data.subscriptionId,
    files: data.files || [],
  };

  return processedData;
};

export function useComplaintDetails(id: string) {
  return useQuery({
    queryKey: ["complaintDetails", id],
    queryFn: () => fetchComplaintDetails(id),
    enabled: !!id,
    staleTime: 60 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
    retry: false
  });
}

export const fetchComplaintDetails = async (id: string) => {
  try {
    const response = await fetch(`/api/complaint/${id}`);
    if (!response.ok) {
      const error = new Error(
        `Failed to fetch complaint details: ${response.statusText}`
      ) as Error & { status: number };
      error.status = response.status;
      throw error;
    }
    const data = await response.json();

    return {
      body: data.body,
      type: data.type,
      subtype: data.subtype,
      subscriptionId: data.subscriptionId,
      distributionCompany: data.distributionCompany,
      documents: data.documents || [],
      response: data.response,
      status: data.status,
    };
  } catch (error) {
    console.error(`Error fetching complaint details for ID (${id}):`, error);
    throw error;
  }
};

export const getComplaintCategories = async () => {
  try {
    const response = await fetch("/api/setting/global/complaints.categories");
    if (!response.ok) {
      const error = new Error(
        `Failed to fetch complaint categories: ${response.statusText}`
      ) as Error & { status: number };
      error.status = response.status;
      throw error;
    }
    const data = await response.json();

    return data.value;
  } catch (error) {
    console.error("Error fetching complaint categories:", error);
    throw error;
  }
};
