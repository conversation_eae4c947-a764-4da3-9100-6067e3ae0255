import React, { Suspense, lazy } from 'react'
import { createLazyFileRoute, useParams } from '@tanstack/react-router'
import { Skeleton } from '@mass/shared/components/ui/skeleton'

const SubscriptionDetailPage = lazy(() => import('@/features/subscriptions/pages/detail'))

function RouteComponent() {
  const params = useParams({ strict: false })
  const uuid = params.uuid
  
  if (!uuid) {
    return null
  }
  
  return (
    <Suspense fallback={
      <div className="w-full h-full flex flex-col space-y-4 p-4">
        <Skeleton className="h-8 w-1/3" />
        <Skeleton className="h-4 w-1/4" />
        <Skeleton className="h-48 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    }>
      <SubscriptionDetailPage subscriptionId={uuid} />
    </Suspense>
  )
}

export const Route = createLazyFileRoute('/_authenticated/subscriptions/$uuid')({
  component: RouteComponent,
})
